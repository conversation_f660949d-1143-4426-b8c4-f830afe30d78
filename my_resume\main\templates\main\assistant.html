{% extends 'main/base.html' %}
{% load static %}

{% block title %}Divine's Ai{% endblock %}
{% block description %}{% endblock %}
{% block keywords %}{% endblock %}

 
<!-- ================================
Start CSS
================================= -->
{% block extend_header %}{% endblock %}

<!-- ================================
End CSS
================================= -->
{% block extend_footer %}{% endblock %}

 
 <!-- ================================
  Start Content
  ================================= -->

{% block content %}

    <main id="chat-page">
        <section style="background-color: #ffffff; flex: 1; display: flex; flex-direction: column;">
          <div style="background-color: #ffffff; border: none; overflow-y: auto;" class="container py-5  chat-container">
            <div class="row h-100">
              <div class="col-12 h-100">
                <!--<div id='chat_container' class="overflow-y-auto grow">-->
                  <!--<ul id='chat_messages' class="flex flex-col justify-end gap-2 p-4">-->
                <div id='chat_container'>
                  <ul id='chat_messages'>
                    <div id="user_message"></div>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          
          
          <div class="chat-form">
            <div class="container">
                <form id="chat_message_form" class="d-flex">
                    {% csrf_token %}
                    {{ form.body }}
                    <button type="submit" aria-label="Send message">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="24" height="24" fill="none" stroke="currentcolor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                            <path d="M2 16 L30 2 16 30 12 20 Z M30 2 L12 20" />
                        </svg>
                    </button>
                </form>
                <footer class="chat_footer">
                    Divine's Assistant isn't perfect. Check the Resume for critical information
                </footer>
            </div>
        </div>
    
        </section>
      </main>    
{% endblock %}



{% block footer %}
{% endblock %}