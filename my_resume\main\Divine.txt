DIVINE Professional experience:

"""
# DIVINE IGBINOBA

**BACKEND DEVELOPER**

📧 <PERSON><EMAIL> | [LinkedIn: DivineUX23](https://linkedin.com/in/DivineUX23) | [GitHub: Divine Igbinoba](https://github.com/DivineIgbinoba)

Over 2 years of web development experience with skills in backend development, DevOps, full-stack development, and AI integrations.

---

## Skills

- **Programming Languages**: Python, JavaScript, TypeScript, C, HTML5, CSS3
- **Frameworks & Libraries**: FastAPI, Django, Flask, Express.js
- **Databases & Caching**: Redis, MySQL, MongoDB
- **DevOps & Tools**: Docker, Git, GitHub
- **AI & Machine Learning**: Gradio-Interface, Langchain, Whisper, ElevenLabs, AssemblyAI
- **Other**: Prompt Engineering, User Experience Design

---

# Professional Experience


**HealthMate** Benin City | Edo State | Nigeria  
BACKEND DEVELOPER JAN 2024- PRESENT

- Architected and developed a scalable backend using FastAPI, MySQL, Langchain, and Google Gemini AI, empowering users to self-diagnose illnesses, connect with medical practitioners, and access emergency services seamlessly —thereby enhancing healthcare accessibility.

**Nigerian National Petroleum Corporation Exploration Production Limited** Benin City | Edo State | Nigeria  
BACKEND DEVELOPER INTERN July 2023- Oct 2023

- Developed a scalable FastAPI backend and MySQL database system for effective asset management, enabling efficient handling and retrieval of over 500 assets and related data, significantly enhancing operational efficiency.

**Polymarq** Remote  
DEVOPS INTERN July 2023- Sept 2023

- Dockerized and deployed AI models on PaperSpace (a subsidiary of DigitalOcean), enabling seamless backend integration and streamlined implementation, which enhanced system performance and efficiency.


---


# Projects and Hackathons

**Lablab.ai Hackathon –** [SPEECH TO SPEECH TRANSLATOR](https://github.com/DivineUX23/Audio-to-Audio-translation)

- Developed a full-stack web application that translates audio or video content from one language to any other, facilitating auditory consumption in the user’s preferred language.
- Technologies Utilized: Employed Flask, JavaScript, HTML5, and Bootstrap for the front-end and back-end development. Integrated Whisper, ElevenLabs, and GPT-3.5 Turbo APIs for core translation functionalities.

**Google Developer Hackathon –** [VIDEO SUMMARIZATION AND Q&A WEB APPLICATION](https://github.com/DivineUX23/chat-with-any-video)

- Designed and developed a full-stack web application that uses AI to generate concise video summaries and enables conversational engagement with AI about the video's content.
- Utilized Flask, JavaScript, HTML5, CSS3, Bootstrap for the front-end and back-end development. Integrated Whisper, YouTube and GPT-3.5 Turbo APIs for essential functionalities.

**Personal Project –** [AUTONOMOUS NOTE TAKING API](https://github.com/DivineUX23/MemoGPT)

- Developed an innovative API that records meetings, lectures, and conversations, leveraging AI to generate concise summaries with speaker detection and enabling conversational engagement with AI about the recorded content.
- Additionally, implemented a user-friendly Gradio interface for testing and integrated seamless payment options for a monthly subscription service.
- Utilized FastAPI, MySQL database, and gradio-interface for the front-end and API development. Integrated Assemblyai, Meta’s llama 2 and mistral 7b APIs for essential functionalities.

**Personal Project –** [WHATSAPP-BASED DIAGNOSIS CHATBOT](https://github.com/DivineUX23/WhatsApp_health_bot)

- Created a WhatsApp-based Diagnosis Chatbot, empowering users to access medical advice anytime, anywhere. This conversational AI diagnoses user illnesses and provides personalized remedies and treatment suggestions sourced from reputable health resources.
- Utilized FastAPI, MySQL database and Langchain for API development. Integrated Google’s Gemini pro, Cohere Command AI, Google search, Tavily search and Twilio APIs for essential functionalities.

**Personal Project –** [API-POWERED SOCIAL MEDIA API](https://github.com/DivineUX23/Express-Typscript)

- Designed and developed a RESTful API for a social media platform, leveraging AI to enhance user posts. Key features include user authentication, post management, AI-driven, post enhancement and others.
- Utilized Express.js, TypeScript, MongoDB, Redis for caching and Socket.io for API real-time communications. Integrated Google’s Gemini pro for AI-powered post enhancement.


---



# Course work

- Harvard CS50’s Web Programming with Python and JavaScript
- Harvard CS50’s Introduction to Programming with Python
- Harvard’s CS50: Introduction to Computer Science
- freeCodeCamp - FastAPI, Express.js (Node.js)
- ALX AFRICA – C, Python, JavaScript, HTML, CSS and SQL
- User experience design by Interaction Design Foundation
"""




ABOUT DIVINE:

"""
As an individual of great ambition and creativity, I am driven by an innate desire to innovate and work hard. My competitive nature fuels my drive for success, while my empathy and thoughtfulness ensure a balanced approach to life. 

I am not one to accept the status quo without question; instead, I constantly challenge existing norms and strive to generate original thoughts.

My intellectual curiosity is vast, as evidenced by my wide range of interests and knowledge. I firmly believe in the importance of expertise and scientific facts, choosing to listen only to those with proven proficiency in their respective fields. This commitment to truth and science underscores all my endeavors.

I hold the conviction that we are the architects of our own purpose. Nothing is predestined, and we have the power to expand our own luck. Moreover, I believe that those who are fortunate should extend their luck to those less fortunate, creating a cycle of shared prosperity.

Above all, I believe in making an impact. Whether big or small, the greatest achievement anyone can aspire to is improving the lives of others and leaving the world better than we found it. This belief guides my actions and decisions, serving as a constant reminder of the potential we each hold to effect meaningful change.


Long-Term Goals:

1. Technological Impact: Strive to create innovative solutions that significantly influence humanity's technological advancement. This includes contributing, either financially or otherwise, to the development and application of quantum computers, nuclear fusion, and space exploration. Specifically, I aim to contribute to the establishment of scientific bases on other planets and moons.

2. Global Health and Nutrition: Work towards ensuring universal access to high-quality healthcare, along with the effective distribution of nutritious food and clean water across all regions of the world.

3. Nigerian Economic Development: A key focus is to substantially enhance the Nigerian production economy, particularly the technology sector. I aim to develop solutions that address both local and global challenges, thereby establishing extensive global solution centers in Nigeria, created by Nigerians, for the benefit of the world.

These long-term objectives serve as the guiding principles for my decisions and actions.


Short-Term Goals:

1. Resource and Capital Accumulation: My immediate goal is to amass resources and capital in various forms, including financial assets, influence, access, skills, expertise, and personal growth.

2. Skill Mastery and Startup Development: This involves honing my skills to mastery levels and establishing solution-driven startups. These endeavors are integral to achieving my long-term goals and are not limited to these pursuits alone.
"""




ABOUT DIVINE:

"""
🔥 Imagine the next Google or Apple is built in Africa...

✨ Imagine Africa countries as centers of technological advancement.


💯 I envision the next generation of global technology companies being built in Africa by Africans.



I aim to develop impactful solutions that solves both global and local problems. 🌍




I'M CURRENTLY... 👨‍💻


🚀 Building robust back-end systems using Django, FastAPI, Flask Python framework. Express JS, Typescript.


🚀 Participating in hackathons to create innovative projects with real-world impact


🚀 Developing useful software products that are FUNCTIONAL, SIMPLE, and ENGAGING 



Insatiably Crafting Impactful Tech



I regularly build projects that you can find here: 

https://replit.com/@DivineIgbinoba





AREAS OF INTEREST ⚙️


🔥 Artificial Intelligence

🔥 Space Exploration

🔥 Product Development

🔥 Quantum computing 

🔥 Nuclear Fusion




GOALS 🎯


💡 Create technologies that improve life on Earth and beyond

🌈 Eliminate Hunger and Poverty

🪐 Enable humans to become an advanced multi-planetary civilization.



I believe through technology and collaboration, we can build a world where all life thrives. 🌱


Our destiny is out there among the stars, Let's build it together.💫 


THE FUTURE IS HERE

"""


