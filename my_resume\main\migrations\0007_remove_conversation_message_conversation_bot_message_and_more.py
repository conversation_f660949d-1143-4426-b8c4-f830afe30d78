# Generated by Django 5.0.4 on 2024-05-17 01:02

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0006_remove_message_conversation_remove_message_timestamp_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='conversation',
            name='message',
        ),
        migrations.AddField(
            model_name='conversation',
            name='bot_message',
            field=models.TextField(default='Thanks'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='conversation',
            name='user_message',
            field=models.TextField(default='good'),
            preserve_default=False,
        ),
        migrations.DeleteModel(
            name='Message',
        ),
    ]
