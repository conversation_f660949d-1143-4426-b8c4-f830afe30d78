{% extends 'main/base.html' %}
{% load static %}

{% block title %}Divine's Home{% endblock %}
{% block description %}{% endblock %}
{% block keywords %}{% endblock %}

 
<!-- ================================
Start CSS
================================= -->
{% block extend_header %}{% endblock %}

<!-- ================================
End CSS
================================= -->
{% block extend_footer %}{% endblock %}

 
 <!-- ================================
  Start Content
  ================================= -->

{% block content %}



<section>
  <div class="bannerSection">
    <div class="container">
      <div class="row g-4 g-md-3 align-items-center">
        <div class="col-md-auto order-md-last">
          <div class="bannerUserImg">
            {% if me.userprofile.avatar %}
              <img src="{{ me.userprofile.avatar.url }}" alt="User Avatar">
            {% else %}
              <img src="" alt="Default Avatar">
            {% endif %}
          </div>
        </div>
        <div class="col-md">
          <div class="bannerContent">
            <!-- <h1 class="xlTitle pb-3">Hi, I’m {{me.first_name|title}}, <br> a {{me.userprofile.title}}</h1> -->
            <h1 class="pb-3">
              <span class="typing"></span>
            </h1>
            
            <p>{{me.userprofile.bio}}</p>
            
            <br>

            <div class="expandable-content collapsed" id="expandable-content">
              <div class="prose max-w-none">
                <p style="margin-bottom: 1rem; line-height: 1.6;">{{ about.detailed_bio|safe }}</p>
              </div>
            </div>
            <!-- Expand/Collapse Button -->

            <div class="flex mt-6">
              <button class="expand-button px-4 py-2 rounded-md bg-blue-500 hover:bg-blue-600 text-white font-semibold transition duration-200 shadow-md"
                      aria-expanded="false" aria-controls="expandable-content">
                <span class="mr-2 expand-text">Show More</span>
                <!--<svg class="w-5 h-5 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>-->
              </button>
            </div>
            
            <div class="bannerBtnCol mt-4">
              <div class="row justify-content-center">
                <div class="col-12 col-sm-auto mb-2 mb-sm-0">
                  <a download href="{{me.userprofile.cv.url}}" class="btn btnPrimary text-white w-100 w-sm-auto">Download Resume</a>
                </div>
                <div class="col-12 col-sm-auto">
                  <a href="{% url 'main:assistant' %}" class="btn btnOutline w-100 w-sm-auto">Chat with My AI</a>
                </div>
              </div>
            </div>
            

          </div>
        </div>
      </div>
    </div>
  </div>
</section>



<!-- templates/work_experience.html -->
<section class="bg-light py-5">
  <div class="container" style="max-width: 960px;">
    <!--<h2 class="display-4 fw-bold text-center text-dark mb-5">Work Experience</h2>-->
    <h2 class="h4 fw-bold text-dark mb-5 text-center">Work Experience</h2>

    
    <div class="position-relative">
      <!-- Timeline line centered -->
      <div class="position-absolute top-0 start-50 translate-middle-x h-100 border-start border-secondary"></div>

      <!-- Experience Items -->
      {% for experience in work_experiences %}
      <div class="position-relative mb-5">
        <div class="d-flex flex-column align-items-center">
          <!-- Timeline dot -->
          <div class="position-absolute top-0 start-50 translate-middle">
            <div class="bg-danger rounded-circle border border-white" style="width: 16px; height: 16px;"></div>
          </div>

          <!-- Content centered -->
          <div class="w-100 mt-4">
            <div class="bg-white rounded shadow p-4 hover-shadow transition-shadow">
              <!-- Company Logo and Title -->
              <div class="d-flex justify-content-center align-items-center mb-3">
                {% if experience.company.logo %}
                <img src="{{ experience.company.logo.url }}" alt="{{ experience.company.name }}" class="img-fluid rounded bg-light p-2" style="width: 48px; height: 48px;">
                {% else %}
                <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="width: 48px; height: 48px;">
                  <span class="fw-bold text-light">{{ experience.company.name|slice:":1" }}</span>
                </div>
                {% endif %}
                <div class="ms-3">
                  <h3 class="h5 fw-semibold text-dark">{{ experience.title }}</h3>
                  <a href="{{ experience.company.website }}" target="_blank" class="text-danger text-decoration-none">{{ experience.company.name }}</a>
                </div>
              </div>

              <!-- Duration and Location -->
              <div class="d-flex justify-content-center align-items-center text-muted mb-3" style="font-size: 0.875rem;">
                <span class="me-2">
                  {{ experience.start_date|date:"M Y" }} - 
                  {% if experience.is_current %}Present{% else %}{{ experience.end_date|date:"M Y" }}{% endif %}
                </span>
                <span>&bull;</span>
                <span class="mx-2">{{ experience.duration }}</span>
                {% if experience.company.location %}
                <span>&bull;</span>
                <span class="ms-2">{{ experience.company.location }}</span>
                {% endif %}
              </div>

              <!-- Employment Type -->
              <span class="badge bg-danger bg-opacity-10 text-danger rounded-pill mb-3">
                {{ experience.get_employment_type_display }}
              </span>

              <!-- Description -->
              <div class="text-dark mb-3">
                {{ experience.description|linebreaks }}
              </div>

              {% if experience.key_achievements %}
              <!-- Key Achievements -->
              <div class="mb-3">
                <h4 class="h6 fw-semibold text-dark mb-2">Key Achievements:</h4>
                <ul class="list-disc list-inside text-dark">
                  {% for achievement in experience.key_achievements %}
                  <li>{{ achievement }}</li>
                  {% endfor %}
                </ul>
              </div>
              {% endif %}

              {% if experience.technologies_used.exists %}
              <!-- Technologies Used -->
              <div class="d-flex flex-wrap gap-2">
                {% for tech in experience.technologies_used.all %}
                <span class="badge bg-secondary bg-opacity-25 text-secondary rounded-pill" style="font-size: 0.75rem;">
                  {{ tech.name }}
                </span>
                {% endfor %}
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>
</section>


<section>
  <div class="py-0">
    <div class="container">
      <div class="row justify-content-center">
        
        <div class="col-md-8">
          <h2 class="h4 fw-bold text-dark mb-5 text-center">Technical Expertise</h2>

          <div class="progressCol">
            <div class="progressCard">
              {% for sk in me.userprofile.skills.all %} 
              {% if not sk.is_key_skill %}
                <div class="mb-3">
                  <label class="form-label">{{ sk.name }}</label>
                  

                  <div class="row g-2 align-items-center">
                    <div class="col">
                      <div class="progress progressStyle">
                        <div class="progress-bar" role="progressbar" style="width: {{sk.score}}%" aria-valuenow="{{sk.score}}" aria-valuemin="0" aria-valuemax="100"></div>
                      </div>
                    </div>
                    <div class="col-auto">
                      <span class="pLbl">{{sk.score}}%</span>
                    </div>
                  </div>


                </div>
              {% endif %}
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>





<!-- templates/tools_section.html -->

<section class="bg-light py-5">
  <div class="container" style="max-width: 1140px;">
    <h2 class="h5 fw-bold text-dark mb-4">Tech Stack & Tools</h2>

      <div class="tools-carousel relative overflow-hidden">
          <div class="tools-track flex animate-scroll">
              {% for tool in tools %}
              <!-- Tool Card -->
              <div class="tool-card flex-none w-48 mx-4 bg-white rounded-lg shadow-sm p-4 flex flex-col items-center">
                {% if tool.icon %}
                <img src="{{ tool.icon.url }}" alt="{{ tool.name }}" class="card-img-top mb-3" style="height: 48px; width: 48px; object-fit: contain;">
              {% else %}
                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mb-3" style="width: 48px; height: 48px;">
                  <span class="text-white">{{ tool.name|slice:":1" }}</span>
                </div>
              {% endif %}
                  
                  <div class="card-body p-0">
                    <h5 class="card-title mb-1" style="font-size: 0.875rem;">{{ tool.name }}</h5>
                    <p class="card-text text-muted" style="font-size: 0.75rem;">{{ tool.get_category_display }}</p>
                  </div>
                </div>
              {% endfor %}
          </div>
      </div>
  </div>
</section>



<section>
  <div class="sectionSpaceSm lightBg">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center pb-3">
        <!--<h4 class="h6 fw-normal">Certificates</h4>-->
        <h2 class="h4 fw-bold text-dark mb-5">Certificates</h2>

        <a href="{% url 'main:portfolios' %}" class="text-decoration-none text-primary">View all</a>
      </div>
      <div class="swiper certificatesSlider">
        <div class="swiper-wrapper">
          {% for c in certificates %}
          {% if c.is_active %}
          <div class="swiper-slide">
            <div class="card shadow-sm p-3">
              <h4 class="h6">
                <a href="javascript:void(0)" class="text-decoration-none">{{ c.name }}</a>
              </h4>
              <ul class="list-unstyled">
                <div class="text-center mb-3">
                  <img src="{{ c.image.url }}" alt="{{ c.name }}" class="img-fluid">
                </div>
                <li>{{ c.title }}</li>
                <li>{{ c.date }}</li>
              </ul>
            </div>
          </div>
          {% endif %}
          {% endfor %}
        </div>
        <!-- Swiper controls can be maintained or replaced with Bootstrap Carousel controls -->
        <!-- If using Swiper JS, ensure it's properly integrated with Bootstrap -->
      </div>
    </div>
  </div>
</section>

<section>
  <div class="sectionSpace">
    <div class="container">
      <div class="portfolioCol">
        <div class="row pb-3">
          <div class="col">
            <!--<h4 class="smText regular">Featured Work</h4>-->
            <h2 class="h4 fw-bold text-dark mb-5">Featured Work</h2>

          </div>
          <div class="col-auto">
            <a href="{% url 'main:portfolios' %}" class="text-decoration-none text-primary">View all</a>
          </div>
        </div>


        {% for p in portfolio %}
        {% if p.is_active %}
        <div class="portfolioCard">
          <div class="row g-4 align-items-center">
            <div class="col-md-auto">
              <div class="portfolioImgCol">
                <a href="{% url 'main:portfolio' slug=p.slug %}"><img src="{{p.image.url}}" alt="..."></a>
              </div>
            </div>
            <div class="col-md">
            
              <div class="card-body">
                <h5 class="card-title">
                  <a href="{% url 'main:portfolio' slug=p.slug %}" class="text-decoration-none">{{ p.name }}</a>
                </h5>
                <ul class="list-inline text-muted">
                  <li class="list-inline-item">{{ p.date }}</li>
                </ul>
                <p class="card-text">{{ p.description }}</p>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
        {% endfor %}



        <div class="testimonialCol">
        <!--<h4 class="smText regular d-block">Testimonials</h4>-->
        <h2 class="h4 fw-bold text-dark mb-5">Testimonials</h2>

        <div class="testimonialSlider">
          <div class="swiper testimonialSwiper">
            <div class="swiper-wrapper">
              {% for t in testimonials %}
              {% if t.is_active %}
              <div class="swiper-slide">
                <div class="testimonialCard">
                  <div class="row align-items-center">
                    <div class="col-sm-auto">
                      <div class="tImgCol">
                        <img src="{{t.thumbnail.url}}" alt="...">
                      </div>
                    </div>
                    <div class="col-sm">
                      <div class="tContentCol">
                        <h4 class="xsTitle bold">{{t.name}} - {{t.role}}</h4>
                        <p>{{t.quote}}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {% endif %}
              {% endfor %}
            </div>
            <div class="test-swiper-button-next swiper-button-next swiperBtnStyle"></div>
            <div class="test-swiper-button-prev swiper-button-prev swiperBtnStyle"></div>
          </div>


        </div>
      </div>
    </div>
  </div>
</section>


<section>
  <div class="py-3 bg-light">
    <div class="container">
      <div class="d-flex justify-content-between align-items-center pb-3">
        <!--<h4 class="h6 fw-normal">Recent Posts</h4>-->
        <h2 class="h4 fw-bold text-dark mb-5 text-center">Recent Posts</h2>

        <a href="{% url 'main:blogs' %}" class="text-decoration-none text-primary">View all</a>
      </div>
      <div class="row g-3">
        {% for b in blogs %}
        {% if b.is_active %}
        <div class="col-lg-6">
          <div class="card shadow-sm">
            <div class="card-body">
              <h5 class="card-title">
                <a href="{% url 'main:blog' slug=b.slug %}" class="text-decoration-none">{{ b.name }}</a>
              </h5>
              <ul class="list-inline text-muted mb-3">
                <li class="list-inline-item">{{ b.timestamp }}</li>
                <li class="list-inline-item">{{ b.author }}</li>
              </ul>
              <p class="card-text">{{ b.description }}</p>
            </div>
          </div>
        </div>
        {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
</section>

{% endblock %}
<!-- ================================
  End Content
  ================================= -->
