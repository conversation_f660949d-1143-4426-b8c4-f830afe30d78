# Generated by Django 5.0.4 on 2024-05-17 01:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0007_remove_conversation_message_conversation_bot_message_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='Assistant',
        ),
        migrations.AlterModelOptions(
            name='conversation',
            options={'verbose_name': 'Assistant', 'verbose_name_plural': 'Assistants'},
        ),
        migrations.RenameField(
            model_name='conversation',
            old_name='timestamp',
            new_name='date',
        ),
        migrations.RemoveField(
            model_name='conversation',
            name='bot_message',
        ),
        migrations.RemoveField(
            model_name='conversation',
            name='user_message',
        ),
        migrations.AddField(
            model_name='conversation',
            name='chat_history',
            field=models.JSONField(blank=True, default='{'),
            preserve_default=False,
        ),
    ]
