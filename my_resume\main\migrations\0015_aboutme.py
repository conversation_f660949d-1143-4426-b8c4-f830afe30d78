# Generated by Django 5.0.4 on 2024-11-14 14:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0014_skill_image_skill_is_key_skill_skill_score'),
    ]

    operations = [
        migrations.CreateModel(
            name='AboutMe',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('headline', models.CharField(max_length=200)),
                ('short_bio', models.TextField(help_text="Brief introduction that's always visible")),
                ('detailed_bio', models.TextField(help_text='Detailed information shown when expanded')),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profile/')),
                ('resume_file', models.FileField(blank=True, null=True, upload_to='documents/')),
                ('github_url', models.URLField(blank=True)),
                ('linkedin_url', models.<PERSON><PERSON><PERSON>ield(blank=True)),
                ('twitter_url', models.U<PERSON><PERSON>ield(blank=True)),
                ('email', models.EmailField(blank=True, max_length=254)),
            ],
            options={
                'verbose_name_plural': 'About Me',
            },
        ),
    ]
