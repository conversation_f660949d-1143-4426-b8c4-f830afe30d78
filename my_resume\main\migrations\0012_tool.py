# Generated by Django 5.0.4 on 2024-11-14 12:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0011_skill_color_scheme'),
    ]

    operations = [
        migrations.CreateModel(
            name='Tool',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('icon', models.ImageField(blank=True, null=True, upload_to='tools/')),
                ('category', models.CharField(choices=[('backend', 'Backend'), ('database', 'Database'), ('devops', 'DevOps'), ('testing', 'Testing'), ('monitoring', 'Monitoring'), ('ai', 'AI'), ('other', 'Other')], max_length=20)),
                ('description', models.TextField(blank=True)),
                ('website_url', models.URLField(blank=True)),
                ('order', models.IntegerField(default=0)),
            ],
            options={
                'ordering': ['category', 'order'],
            },
        ),
    ]
