{% load static %}

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@700&family=Open+Sans:wght@400;600&display=swap" rel="stylesheet">

    <title>{% block title %}{% endblock %}</title>
    <meta name="author" content="Divine Backend Dev">
    <link rel="canonical" href="{{request.path}}"/>
    <link rel="home" href="{% url 'main:home' %}"/>
    <meta name="description" content="{% block description %}{% endblock %}">
    <meta name="keywords" content="{% block keywords %}{% endblock %}">

    <link rel="shortcut icon" type="image/x-icon" href="{% static 'images/_MGL9542 (4) (1).jpg' %}">
    <link rel="apple-touch-icon" type="image/jpg" href="{% static 'images/_MGL9542 (4) (1).jpg' %}">

    <!-- ================================
    Start CSS
    ================================= -->
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="https://unpkg.com/swiper@7.0.5/swiper-bundle.min.css">
    <link href="{% static 'css/style.css' %}" rel="stylesheet">
    {% block extend_header %}{% endblock %}
    <!-- ================================
    End CSS
    ================================= -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ENjdO4Dr2bkBIFxQpeo3lKWBj0K5CXvvF6fG7hbFf9Cejl8CS5gG83cmK1G9gO/a" crossorigin="anonymous"></script>



      <!-- Your Custom CSS -->
      <style>
        /* Custom Link Colors */
        a {
            color: #002C54;
        }
        a:hover, a:focus {
            color: #002C54;
            text-decoration: underline; /* Optional: Adds underline on hover */
        }
        
        a:not(.btn):not(.nav-link) {
            /* Ensures buttons and navigation links retain their Bootstrap styles */
            color: #002C54;
        }      

        .btnPrimary {
          background-color: #002855;
          color: !important;
          padding: 0.75rem 1.5rem;
          border: 2px solid #002855;
          border-radius: 4px;
          font-size: 1.25rem;
          transition: all 0.3s ease;
          text-decoration: none;
        }

        .btnPrimary:hover {
          background-color: #003872;
          color: white !important;
          border-color: #003872;
        }

        .btnPrimary:focus,
        .btnPrimary:active {
          color: white !important;
        }

        .btnOutline {
          background-color: transparent;
          color: #002855;
          padding: 0.75rem 1.5rem;
          border: 2px solid #002855;
          border-radius: 4px;
          font-size: 1.25rem;
          transition: all 0.3s ease;
          text-decoration: none;
        }

        .btnOutline:hover {
          background-color: #002855;
          color: white;
        }

        .bannerBtnCol {
          max-width: 800px;
          margin: 0 auto;
        }

        .bannerUserImg img {
          transition: transform 0.3s;
        }
        .bannerUserImg:hover img {
          transform: scale(1.1);
        }

        .expandable-content {
          transition: max-height 0.5s ease, opacity 0.5s ease;
          overflow: hidden;
          max-height: 270px; /* Adjust based on desired preview height */
          position: relative;
          opacity: 1;
        }

        .expandable-content::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 100px; /* Height of the fade effect */
          background: linear-gradient(to bottom, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
          pointer-events: none;
          transition: opacity 0.5s ease;
        }

        .expandable-content.expanded::after {
          display: none;
        }

        .expandable-content.expanded {
          max-height: 10000px; /* A large enough value to accommodate the full content */
        }

        /* Adjusted expand-button styles */
        .expand-button {
        animation: bounce 1.5s infinite;
        padding: 0.5rem;
        border-radius: 9999px; /* For fully rounded button */
        background-color: #002C54; /* Example primary color */
        border: none; /* Remove border if your btnPrimary handles it */
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color 0.3s ease;
        }
        .flex {
        display: flex;
        justify-content: center; /* Centers items horizontally */
        align-items: center;     /* Centers items vertically (optional) */
        }

        .expand-button:hover {
        background-color: #00184c; /* Darker shade on hover */
        }

        @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-5px);
        }
        60% {
          transform: translateY(-3px);
        }
        }

        /* Ensure btnPrimary and btnOutline retain their original styles */
        .btnPrimary {
        /* Your existing btnPrimary styles */
        }

        .btnOutline {
        /* Your existing btnOutline styles */
        }

        /* Optional: Adjust button focus styles for accessibility */
        .expand-button:focus {
        outline: 2px solid #002C54;
        outline-offset: 2px;
        }

        /* Button Focus Styles for Accessibility */
        .expand-button:focus {
        outline: 2px solid #005fcc;
        outline-offset: 2px;
        }

        /* Preserve Inner Content Formatting */
        .prose {
        /* Ensure that child elements like headings, lists, etc., are styled appropriately */
        /* You can customize typography here or integrate a typography library like Tailwind CSS's prose plugin */
        font-size: 1rem;
        line-height: 1.6;
        color: #333;
        }

        /* Optional: Customize Prose Elements */
        .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
        margin-top: 1.5em;
        margin-bottom: 0.5em;
        font-weight: bold;
        }

        .prose p {
        margin-bottom: 1em;
        }

        .prose ul, .prose ol {
        margin-bottom: 1em;
        padding-left: 1.5em;
        }

        .prose a {
        color: #007bff;
        text-decoration: underline;
        }

        .prose a:hover {
        color: #0056b3;
        }



          /* Scroll Animation */
          @keyframes scroll {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100%); }
        }

        /* Carousel track styling */
        .tools-carousel {
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
        }

        /* Infinite scroll on the tools-track */
        .tools-track {
            display: flex;
            animation: scroll 7s linear infinite;
        }

        /* Duplicate items to ensure smooth scrolling */
        .tools-track > div {
            flex: none;
        }

        /* New Hero Section Styles */
        .hero-section {
            padding: 120px 0 80px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            position: relative;
            overflow: hidden;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: #6c757d;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .hero-buttons .btn {
            padding: 12px 30px;
            font-size: 1.1rem;
            border-radius: 8px;
        }

        .hero-image-container {
            position: relative;
            height: 500px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .hero-illustration {
            position: relative;
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #002C54 0%, #003872 100%);
            border-radius: 50% 20% 50% 20%;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-avatar {
            width: 280px;
            height: 280px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .floating-element {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .element-1 {
            width: 60px;
            height: 60px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .element-2 {
            width: 40px;
            height: 40px;
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }

        .element-3 {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 5%;
            animation-delay: 4s;
        }

        .element-4 {
            width: 30px;
            height: 30px;
            bottom: 10%;
            right: 10%;
            animation-delay: 1s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        /* Logo Styles */
        .logo-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #002C54;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .logo-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .logo-text {
            color: #002C54;
            font-size: 1.25rem;
        }

        .say-hello-btn {
            background: #002C54;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            color: white;
            font-weight: 500;
        }

        .say-hello-btn:hover {
            background: #003872;
            color: white;
        }

        /* Case Studies Section */
        .case-studies-section {
            background: #f8f9fa;
        }

        .case-studies-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #002C54;
            margin: 0;
        }

        .case-studies-tags .tag {
            background: #e9ecef;
            color: #6c757d;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-right: 10px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .hero-illustration {
                width: 300px;
                height: 300px;
            }

            .hero-avatar {
                width: 200px;
                height: 200px;
            }
        }

    </style>
    
  </head>
  <body>
    {% include 'main/partials/messages.html' %}
    {% include 'main/partials/nav.html' %}

    {% block content %}{% endblock %}
    <!-- ================================
    End Content
    ================================= -->

    {% block footer %}
    {% include 'main/partials/footer.html' %}
    {% endblock %}

    <!-- ================================
    Start Scripts
    ================================= -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ENjdO4Dr2bkBIFxQpeo3lKWBj0K5CXvvF6fG7hbFf9Cejl8CS5gG83cmK1G9gO/a" crossorigin="anonymous"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

    <script src="https://unpkg.com/swiper@7.0.5/swiper-bundle.min.js"></script>
    <script src="{% static 'js/script.js' %}"></script>
    <script src="https://cdn.jsdelivr.net/npm/typewriter-effect@2.18.0/dist/core.min.js"></script>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script src="https://unpkg.com/showdown/dist/showdown.min.js"></script>
  
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>

    

    <script>
     document.addEventListener("DOMContentLoaded", function () {
        // Initialize any interactive elements here
        console.log("Page loaded successfully");
      });


     
      /*function startTypingEffect() {
        const texts = ["I think you know me", "I'm a {{me.userprofile.title}}", "I'm {{me.first_name|title}}."];
        let count = 0;
        let index = 0;
        let currentText = '';
        let letter = '';
        const typingElement = document.querySelector('.typing');
      
        function type() {
          if (count === texts.length) {
            count = 0;
          }
          currentText = texts[count];
          letter = currentText.slice(0, ++index);
      
          typingElement.textContent = letter;
          if (letter.length === currentText.length) {
            count++;
            index = 0;
            setTimeout(type, 2000); // Wait 2 seconds before starting to delete
          } else {
            setTimeout(type, 80); // Time between each letter is typed
          }
        }
        type(); // Start the typing effect
      }*/
      
    </script>
  
    {% block extend_footer %}{% endblock %}
    <!-- ================================
    End Scripts
    ================================= -->
  </body>
</html>
