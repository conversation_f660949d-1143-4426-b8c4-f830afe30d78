/* home.css */


.expandable-content-container {
  position: relative;
  max-height: 8rem; /* Adjust height for the visible teaser */
  overflow: hidden;
}

.fade-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2rem;
  background: linear-gradient(to top, white, transparent);
  pointer-events: none;
}

.expandable-content {
  max-height: 8rem;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.expand-button {
  animation: bounce 1.5s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}



@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

.animate-scroll {
    animation: scroll 30s linear infinite;
}

.tools-carousel:hover .animate-scroll {
    animation-play-state: paused;
}

.hero {
    padding: 100px 0;
    background-color: #f8f9fa;
  }
  
  .hero-title {
    font-size: 3rem;
    margin-bottom: 20px;
  }
  
  .hero-bio {
    font-size: 1.2rem;
    margin-bottom: 30px;
  }
  
  .hero-cta .btn {
    margin-right: 15px;
  }
  
  .hero-image img {
    max-width: 100%;
    border-radius: 50%;
  }
  
  .section-title {
    margin-bottom: 30px;
  }
  
  .skills, .certificates, .featured-work, .testimonials, .recent-posts {
    padding: 80px 0;
  }
  
  .skill-item, .certificate-card, .work-item, .testimonial-card, .blog-card {
    margin-bottom: 30px;
  }
  
  /* Add more specific styles as needed */